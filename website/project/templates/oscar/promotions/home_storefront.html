{% extends "oscar/layout_storefront.html" %}
{% load i18n %}
{% load cache %}
{% load promotion_tags %}
{% load static %}

{% block title %}
    {{ shop_name }} | {{ shop_tagline }}
{% endblock %}

{% block content_wrapper %}
{% include "oscar/promotions/partials/home_vehicle_category_filter.html" %}
<div class="container mx-auto px-4 py-4">
    <!-- Alpine.js component for tab management -->
    <div x-cloak x-data="{ activeTab: 'manufacturers' }">
        <!-- Tab Buttons -->
        <div class="mb-6 flex justify-center border-b border-gray-200">
            <button
                @click="activeTab = 'manufacturers'"
                :class="{ 'border-blue-800 text-blue-800': activeTab === 'manufacturers', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'manufacturers' }"
                class="py-4 px-6 block font-normal leading-5 border-b-2 focus:outline-none transition-colors duration-150 ease-in-out">
                {% trans 'Manufacturers' %}
            </button>
            <button
                @click="activeTab = 'categories'"
                :class="{ 'border-blue-800 text-blue-800': activeTab === 'categories', 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300': activeTab !== 'categories' }"
                class="py-4 px-6 block font-normal leading-5 border-b-2 focus:outline-none transition-colors duration-150 ease-in-out">
                {% trans 'Categories' %}
            </button>
        </div>

        <!-- Manufacturers Section -->
        <section x-show="activeTab === 'manufacturers'" x-cloak class="mb-12">

            <div x-data="{ showAll: false }" class="flex flex-wrap justify-center -mx-2">
                {% for manuf in manufacturers_showcase %}
                    <div x-show="({{ manuf.number_of_products }} > 0) && (({{ manuf.number_of_products }} >= 400) || showAll)" x-cloak class="w-1/3 sm:w-1/4 md:w-1/6 lg:w-1/8 xl:w-1/8 px-2 mb-4">
                        <a href="{% url 'manufacturer-catalogue' manufacturer_slug=manuf.brand_slug %}" class="group">
                            <div class="flex flex-col items-center">
                                <!-- Logo container with fixed aspect ratio -->
                                <div class="w-full aspect-square bg-white rounded-lg shadow border border-gray-200 mb-2 overflow-hidden min-h-[80px]">
                                    <div class="w-full h-full flex items-center justify-center p-2">
                                        <picture>
                                            <source srcset="{{ MEDIA_URL }}manufacturers/logo_{{ manuf.brand_slug|lower }}.webp" type="image/webp">
                                            <img
                                              class="max-w-full max-h-full object-contain group-hover:scale-110 transition-transform duration-500 ease-in-out"
                                              src="{{ MEDIA_URL }}manufacturers/logo_{{ manuf.brand_slug|lower }}.png"
                                              alt="{{ manuf.brand }}"
                                              loading="lazy"
                                              decoding="async"/>
                                        </picture>
                                    </div>
                                </div>
                                <!-- Name and count next to each other -->
                                <div class="text-center flex flex-col sm:flex-row items-center justify-center">
                                    <h3 class="text-xs lg:text-sm font-normal text-gray-500 group-hover:text-blue-700 transition-colors">{{ manuf.brand|upper }}</h3>
                                    <span class="mt-1 sm:mt-0 sm:ml-2 bg-gray-200 text-gray-700 text-xs rounded-full px-2 py-0.5 flex-shrink-0 inline-block">{{ manuf.number_of_products }}</span>
                                </div>
                            </div>
                        </a>
                    </div>
                {% endfor %}

                <!-- Toggle All Manufacturers Tile -->
                <div x-cloak class="w-1/3 sm:w-1/4 md:w-1/6 lg:w-1/8 xl:w-1/8 px-2 mb-4">
                    <button type="button" @click="showAll = !showAll" class="group w-full h-full focus:outline-none">
                        <div class="flex flex-col items-center justify-center h-full">
                            <div class="w-full aspect-square bg-white rounded-lg shadow border border-gray-200 mb-2 overflow-hidden flex items-center justify-center">
                                <span class="text-base sm:text-xl text-blue-800 font-bold group-hover:scale-110 transition-transform duration-500 ease-in-out" x-text="showAll ? '-' : '{% trans "All" %}'"></span>
                            </div>
                            <div class="text-center flex flex-col sm:flex-row items-center justify-center">
                                <h3 class="text-xs lg:text-sm font-normal text-gray-500 group-hover:text-blue-700 transition-colors" x-text="showAll ? '{% trans "Show less" %}' : '{% trans "Show all" %}'"></h3>
                                <span class="mt-1 sm:mt-0 sm:ml-2 text-xs py-0.5 text-transparent">&nbsp;</span>
                            </div>
                        </div>
                    </button>
                </div>
            </div>
        </section>

        <!-- Categories Section -->
        <section x-show="activeTab === 'categories'" x-cloak>

            <div class="flex flex-wrap justify-center -mx-2">
                {% for category in categories_showcase %}
                <div class="w-1/3 sm:w-1/4 md:w-1/6 lg:w-1/8 xl:w-1/8 px-2 mb-4">
                    <a href="{{ category.get_absolute_url }}" class="group">
                        <div class="flex flex-col items-center">
                            <!-- Category image container with fixed aspect ratio -->
                            <div class="w-full aspect-square bg-white rounded-lg shadow border border-gray-200 mb-2 overflow-hidden min-h-[80px]">
                                <div class="w-full h-full flex items-center justify-center p-2">
                                    <img
                                      class="max-w-full max-h-full object-contain group-hover:scale-110 transition-transform duration-500 ease-in-out"
                                      src="{{ category.image_url }}"
                                      alt="{{ category.title }}"
                                      loading="lazy"
                                      decoding="async"/>
                                </div>
                            </div>
                            <!-- Name and count next to each other -->
                            <div class="text-center flex flex-col sm:flex-row items-center justify-center">
                                <h3 class="text-xs lg:text-sm font-normal text-gray-500 group-hover:text-blue-700 transition-colors">{{ category.name }}</h3>
                                <span class="mt-1 sm:mt-0 sm:ml-2 bg-gray-200 text-gray-700 text-xs rounded-full px-2 py-0.5 flex-shrink-0 inline-block">{{ category.number_of_products }}</span>
                            </div>
                        </div>
                    </a>
                </div>
                {% endfor %}
            </div>
        </section>
    </div>
</div>

<!-- Customer Reviews Section -->
{% if reviews %}
<div x-cloak class="py-4 mt-4 mb-8" x-data="{ isOpen: window.innerWidth >= 768 }">
    <div class="container mx-auto px-4">
        <div class="text-center mb-8">
            <h2 class="text-xl font-semibold text-gray-800">{% trans 'Customer Reviews' %}</h2>
            <!-- Toggle Button - visible only on mobile (screens smaller than md) -->
            <button @click="isOpen = !isOpen"
                    class="md:hidden mt-4 px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-150 ease-in-out">
                <span x-text="isOpen ? '{% trans "Hide Reviews" %}' : '{% trans "Show Reviews" %}'"></span>
                <svg x-show="!isOpen" class="inline-block ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path></svg>
                <svg x-show="isOpen" class="inline-block ml-2 w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 15l7-7 7 7"></path></svg>
            </button>
        </div>

        <div x-show="isOpen"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0 -translate-y-4 max-h-0"
             x-transition:enter-end="opacity-100 translate-y-0 max-h-screen"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100 translate-y-0 max-h-screen"
             x-transition:leave-end="opacity-0 -translate-y-4 max-h-0"
             class="overflow-hidden">
            <div x-data="{
                reviewsLoaded: {{ reviews|length }},
                totalReviews: {{ total_reviews_count }},
                isLoading: false,
                loadMoreReviews() {
                    if (this.isLoading) return;

                    this.isLoading = true;
                    fetch('{% url 'promotions:load-more-reviews' %}?offset=' + this.reviewsLoaded + '&limit=6')
                        .then(response => response.json())
                        .then(data => {
                            // Append the new reviews to the container
                            const reviewsContainer = document.getElementById('reviews-container');
                            reviewsContainer.insertAdjacentHTML('beforeend', data.html);

                            // Update the counters
                            this.reviewsLoaded += 6;
                            this.totalReviews = data.total;
                            this.isLoading = false;
                        })
                        .catch(error => {
                            console.error('Error loading more reviews:', error);
                            this.isLoading = false;
                        });
                }
            }">
            <!-- Reviews Cards -->
            <div id="reviews-container" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {% include "oscar/promotions/partials/review_cards.html" with reviews=reviews start_index=0 %}
            </div>

            <!-- Load More Button -->
            <div class="text-center mt-8 mb-4" x-show="reviewsLoaded < totalReviews">
                <button
                    @click="loadMoreReviews()"
                    :disabled="isLoading"
                    class="inline-flex items-center px-4 py-2 border border-blue-600 text-blue-600 rounded-md hover:bg-blue-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-colors duration-150 ease-in-out disabled:opacity-50 disabled:cursor-not-allowed"
                >
                    <span x-show="!isLoading">{% trans 'Load More Reviews' %}</span>
                    <span x-show="isLoading">{% trans 'Loading...' %}</span>
                    <svg x-show="!isLoading" class="ml-2 -mr-1 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                    </svg>
                    <svg x-show="isLoading" class="ml-2 -mr-1 h-4 w-4 animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                </button>
            </div>
        </div> <!-- Closing inner x-data div -->
    </div> <!-- Closing x-show div -->
</div>
{% endif %}
{% endblock content_wrapper %}

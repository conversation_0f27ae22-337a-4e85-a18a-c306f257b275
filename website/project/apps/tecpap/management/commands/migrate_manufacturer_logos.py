import os
import shutil
from django.core.management.base import BaseCommand
from django.conf import settings
from project.apps.tecpap.models import Manufacturer
from PIL import Image


class Command(BaseCommand):
    help = 'Migrate manufacturer logos from static/img/cars/ to media/manufacturers/'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be moved without actually moving files',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']

        # Define source and destination directories
        source_dir = os.path.join(settings.PROJECT_MODULE, "static", "img", "cars")
        dest_dir = os.path.join(settings.MEDIA_ROOT, "manufacturers")

        # Create destination directory if it doesn't exist
        if not dry_run and not os.path.exists(dest_dir):
            os.makedirs(dest_dir)
            self.stdout.write(f"Created directory: {dest_dir}")

        # Get all manufacturers
        manufacturers = Manufacturer.objects.all()
        moved_count = 0
        not_found_count = 0

        for manufacturer in manufacturers:
            logo_filename = f"logo_{manufacturer.slug.lower()}.png"
            source_path = os.path.join(source_dir, logo_filename)
            dest_path = os.path.join(dest_dir, logo_filename)

            if os.path.exists(source_path):
                if dry_run:
                    webp_filename = f"logo_{manufacturer.slug.lower()}.webp"
                    webp_dest_path = os.path.join(dest_dir, webp_filename)
                    self.stdout.write(f"Would move: {source_path} -> {dest_path}")
                    self.stdout.write(f"Would create WebP: {webp_dest_path}")
                else:
                    try:
                        # Copy PNG version
                        shutil.copy2(source_path, dest_path)

                        # Create WebP version
                        webp_filename = f"logo_{manufacturer.slug.lower()}.webp"
                        webp_dest_path = os.path.join(dest_dir, webp_filename)

                        with Image.open(source_path) as img:
                            img.save(webp_dest_path, 'WEBP', quality=85)

                        self.stdout.write(f"Moved: {logo_filename} (+ WebP version)")
                        moved_count += 1
                    except Exception as e:
                        self.stdout.write(
                            self.style.ERROR(f"Error moving {logo_filename}: {e}")
                        )
            else:
                self.stdout.write(
                    self.style.WARNING(f"Logo not found for {manufacturer.brand}: {logo_filename}")
                )
                not_found_count += 1

        # Summary
        if dry_run:
            self.stdout.write(
                self.style.SUCCESS(f"DRY RUN: Would move {moved_count} logos, {not_found_count} not found")
            )
        else:
            self.stdout.write(
                self.style.SUCCESS(f"Successfully moved {moved_count} logos, {not_found_count} not found")
            )

            # Also check static_collected directory and move from there if needed
            static_collected_dir = os.path.join(settings.STATIC_ROOT, "img", "cars")
            if os.path.exists(static_collected_dir):
                self.stdout.write(f"\nAlso checking static_collected directory: {static_collected_dir}")
                additional_moved = 0

                for manufacturer in manufacturers:
                    logo_filename = f"logo_{manufacturer.slug.lower()}.png"
                    source_path = os.path.join(static_collected_dir, logo_filename)
                    dest_path = os.path.join(dest_dir, logo_filename)

                    # Only copy if destination doesn't exist yet
                    if os.path.exists(source_path) and not os.path.exists(dest_path):
                        try:
                            # Copy PNG version
                            shutil.copy2(source_path, dest_path)

                            # Create WebP version
                            webp_filename = f"logo_{manufacturer.slug.lower()}.webp"
                            webp_dest_path = os.path.join(dest_dir, webp_filename)

                            with Image.open(source_path) as img:
                                img.save(webp_dest_path, 'WEBP', quality=85)

                            self.stdout.write(f"Moved from static_collected: {logo_filename} (+ WebP version)")
                            additional_moved += 1
                        except Exception as e:
                            self.stdout.write(
                                self.style.ERROR(f"Error moving from static_collected {logo_filename}: {e}")
                            )

                if additional_moved > 0:
                    self.stdout.write(
                        self.style.SUCCESS(f"Additionally moved {additional_moved} logos from static_collected")
                    )
